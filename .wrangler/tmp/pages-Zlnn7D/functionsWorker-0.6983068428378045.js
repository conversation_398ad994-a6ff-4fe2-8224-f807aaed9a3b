var __defProp = Object.defineProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });

// _middleware.js
var OFFER_ID_LOG = "virex_all_one";
var PATH_VARIANT_B = `/index_b.html`;
var TRUSTED_USER_VARIANTS_CONFIG = {
  enabled: true,
  // Включить/выключить распределение на подварианты для доверенных
  identifierSource: "ip_ua",
  // 'ip' или 'ip_ua'
  // ВАЖНО: Если enabled = true, должен быть хотя бы один вариант.
  // `/index_a.html` теперь является одним из вариантов в этой группе.
  variants: [
    { path: `/index_a1.html`, weight: 34, name: "a1" },
    { path: `/index_a2.html`, weight: 33, name: "a2" },
    { path: `/index_a3.html`, weight: 33, name: "a3" }
  ]
};
var filterSettings = {
  FORCE_VARIANT_B: false,
  country: {
    enabled: false,
    logic: "allowed",
    list: ["CO"],
    sendUnknownToB: true
  },
  asOrganization: {
    enabled: true,
    disallowed: [
      "Google",
      "Amazon",
      "AWS",
      "Microsoft",
      "Azure",
      "OVH",
      "Hetzner",
      "DigitalOcean",
      "Linode",
      "Cloudflare",
      "AS-CHOOPA",
      "Psychz",
      "FranTech",
      "Ace Data Centers",
      "Censys",
      "ColoCrossing",
      "Contabo",
      "Andhika Pratama Sanggoro",
      "Driftnet",
      "Facebook",
      "Iway",
      "Kprohost",
      "Maroc Telecom",
      "MXCLOUD",
      "NordVPN",
      "Octopus Web",
      "Onyphe",
      "ReliableSite",
      "Saygin Host",
      "Sovy Cloud",
      "sprint",
      "AMAZON-02",
      "MICROSOFT-CORP-MSN-AS-BLOCK"
    ]
  },
  clientTrust: {
    enabled: false,
    blockIfScoreLessThan: 15
  },
  os: {
    enabled: true,
    logic: "allowed",
    list: ["Android", "iOS"]
  },
  isWebview: {
    enabled: true,
    required: true
  },
  isFacebookApp: {
    enabled: true,
    required: true
  }
};
async function getNumericHash(inputString) {
  const encoder = new TextEncoder();
  const data = encoder.encode(inputString);
  const hashBuffer = await crypto.subtle.digest("SHA-1", data);
  const hashArray = new Uint8Array(hashBuffer);
  let value = 0;
  for (let i = 0; i < 4; i++) {
    value = value << 8 | hashArray[i];
  }
  return Math.abs(value);
}
__name(getNumericHash, "getNumericHash");
async function chooseWeightedSubVariantConfig(identifier, config) {
  if (!config.enabled || !config.variants || config.variants.length === 0) {
    console.warn("[chooseWeightedSubVariantConfig] Sub-variant splitting is disabled or no variants configured.");
    return null;
  }
  if (config.variants.length === 1) {
    return config.variants[0];
  }
  const userHash = await getNumericHash(identifier);
  const totalWeight = config.variants.reduce((sum, variant) => sum + variant.weight, 0);
  if (totalWeight === 0) {
    console.warn("[chooseWeightedSubVariantConfig] Total weight for sub-variants is 0. Cannot choose.");
    return null;
  }
  let hashThreshold = userHash % totalWeight;
  for (const variant of config.variants) {
    if (hashThreshold < variant.weight) {
      return variant;
    }
    hashThreshold -= variant.weight;
  }
  console.warn("[chooseWeightedSubVariantConfig] Fallback: choosing last variant due to unexpected hashThreshold.");
  return config.variants.length > 0 ? config.variants[config.variants.length - 1] : null;
}
__name(chooseWeightedSubVariantConfig, "chooseWeightedSubVariantConfig");
async function onRequest(context) {
  const { request, env, waitUntil, next } = context;
  const url = new URL(request.url);
  const path = url.pathname;
  const baseAbTestPaths = ["/", "/index", "/index.html", "/index_b", "/index_a", "/index_a1", "/index_a2", "/index_a3", "/index_b.html", "/index_a.html", "/index_a1.html", "/index_a2.html", "/index_a3.html"];
  const dynamicAbTestPaths = TRUSTED_USER_VARIANTS_CONFIG.variants.map((v) => v.path);
  const abTestPaths = [.../* @__PURE__ */ new Set([...baseAbTestPaths, ...dynamicAbTestPaths, PATH_VARIANT_B])];
  if (abTestPaths.includes(path)) {
    const timestamp = (/* @__PURE__ */ new Date()).toISOString();
    const cfRay = request.headers.get("cf-ray") || `no-ray-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const requestUrl = request.url;
    const cfData = request.cf || {};
    const countryCode = cfData.country || null;
    const clientIp = request.headers.get("CF-Connecting-IP") || "UNKNOWN_IP";
    const asOrganization = cfData.asOrganization || "Unknown AS Organization";
    const clientTrustScore = cfData.clientTrustScore || null;
    const userAgentRaw = request.headers.get("User-Agent") || "";
    const headersObject = {};
    for (const [key, value] of request.headers) {
      headersObject[key] = value;
    }
    const allHeadersRaw = JSON.stringify(headersObject);
    const cfObjectRaw = JSON.stringify(cfData);
    const uaInfo = parseUserAgent(userAgentRaw);
    let clientTrustCategory = "unknown";
    if (clientTrustScore !== null) {
      if (clientTrustScore < 15)
        clientTrustCategory = "very_low_trust";
      else if (clientTrustScore < 50)
        clientTrustCategory = "low_trust";
      else
        clientTrustCategory = "high_trust";
    }
    let targetVariantPath;
    let filterPassedReason = "initial";
    let variantDecisionForLog;
    if (filterSettings.FORCE_VARIANT_B) {
      targetVariantPath = PATH_VARIANT_B;
      filterPassedReason = "forced_variant_b";
      variantDecisionForLog = "b";
    } else {
      let reasonForB = null;
      if (filterSettings.country.enabled) {
        const countryKnown = countryCode && countryCode !== "T1";
        if (!countryKnown && filterSettings.country.sendUnknownToB) {
          reasonForB = "country_unknown";
        } else if (countryKnown) {
          const listContainsCountry = filterSettings.country.list.includes(countryCode);
          if (filterSettings.country.logic === "allowed" && !listContainsCountry) {
            reasonForB = "country_not_in_allowed_list";
          } else if (filterSettings.country.logic === "disallowed" && listContainsCountry) {
            reasonForB = "country_in_disallowed_list";
          }
        }
      }
      if (!reasonForB && filterSettings.asOrganization.enabled && asOrganization) {
        const lowerCaseAsOrg = asOrganization.toLowerCase();
        if (filterSettings.asOrganization.disallowed.some((org) => lowerCaseAsOrg.includes(org.toLowerCase()))) {
          reasonForB = "as_organization_blocked";
        }
      }
      if (!reasonForB && filterSettings.clientTrust.enabled && clientTrustScore !== null) {
        if (clientTrustScore < filterSettings.clientTrust.blockIfScoreLessThan) {
          reasonForB = `client_trust_score_too_low (${clientTrustScore})`;
        }
      }
      if (!reasonForB && filterSettings.os.enabled && uaInfo.os.name !== "unknown") {
        const lowerCaseOSList = filterSettings.os.list.map((os) => os.toLowerCase());
        const currentOSLower = uaInfo.os.name.toLowerCase();
        const listContainsOS = lowerCaseOSList.includes(currentOSLower);
        if (filterSettings.os.logic === "allowed" && !listContainsOS) {
          reasonForB = "os_not_in_allowed_list";
        } else if (filterSettings.os.logic === "disallowed" && listContainsOS) {
          reasonForB = "os_in_disallowed_list";
        }
      }
      if (!reasonForB && filterSettings.isWebview.enabled) {
        if (uaInfo.browser.isWebview !== filterSettings.isWebview.required) {
          reasonForB = `is_webview_mismatch (required: ${filterSettings.isWebview.required}, actual: ${uaInfo.browser.isWebview})`;
        }
      }
      if (!reasonForB && filterSettings.isFacebookApp.enabled && filterSettings.isWebview.required && uaInfo.browser.isWebview) {
        if (uaInfo.browser.isFacebookApp !== filterSettings.isFacebookApp.required) {
          reasonForB = `facebook_app_mismatch (required: ${filterSettings.isFacebookApp.required}, actual: ${uaInfo.browser.isFacebookApp})`;
        }
      }
      if (reasonForB) {
        targetVariantPath = PATH_VARIANT_B;
        filterPassedReason = reasonForB;
        variantDecisionForLog = "b";
      } else {
        filterPassedReason = "passed_all_filters";
        let chosenTrustedVariant = null;
        if (TRUSTED_USER_VARIANTS_CONFIG.enabled && TRUSTED_USER_VARIANTS_CONFIG.variants && TRUSTED_USER_VARIANTS_CONFIG.variants.length > 0) {
          let identifier = TRUSTED_USER_VARIANTS_CONFIG.identifierSource === "ip_ua" ? clientIp + userAgentRaw : clientIp;
          chosenTrustedVariant = await chooseWeightedSubVariantConfig(identifier, TRUSTED_USER_VARIANTS_CONFIG);
        }
        if (chosenTrustedVariant && chosenTrustedVariant.path) {
          targetVariantPath = chosenTrustedVariant.path;
          variantDecisionForLog = chosenTrustedVariant.name || chosenTrustedVariant.path.substring(chosenTrustedVariant.path.lastIndexOf("/") + 1).replace(".html", "").replace("index_", "");
          filterPassedReason += `_to_split_${variantDecisionForLog}`;
        } else {
          console.error(`[${cfRay}] CRITICAL: Trusted user, but no sub-variant chosen. Fallback to B. Check TRUSTED_USER_VARIANTS_CONFIG.`);
          targetVariantPath = PATH_VARIANT_B;
          filterPassedReason = "trusted_user_no_subvariant_fallback_to_b";
          variantDecisionForLog = "b_fallback_trusted";
        }
      }
    }
    let responseToClient;
    try {
      const assetUrl = new URL(targetVariantPath, request.url);
      const assetResponse = await env.ASSETS.fetch(assetUrl.toString());
      if (assetResponse.ok) {
        responseToClient = new Response(assetResponse.body, assetResponse);
        responseToClient.headers.set("Content-Type", "text/html;charset=UTF-8");
      } else {
        filterPassedReason = `asset_fetch_error_(${assetResponse.status})_for_path_${targetVariantPath}`;
        variantDecisionForLog = `error_fetch_${assetResponse.status}`;
        console.error(`[${cfRay}] Asset fetch error ${assetResponse.status} for ${targetVariantPath}.`);
        responseToClient = new Response(
          `Error: Content not found (status ${assetResponse.status}) for ${targetVariantPath}. Ray ID: ${cfRay}`,
          { status: assetResponse.status, headers: { "Content-Type": "text/html;charset=UTF-8" } }
        );
      }
    } catch (error) {
      console.error(`[${cfRay}] Error fetching asset ${targetVariantPath}: ${error.message}`, error.stack);
      filterPassedReason = `asset_fetch_exception_for_path_${targetVariantPath}`;
      variantDecisionForLog = `error_exception`;
      responseToClient = new Response(
        `Server Error. Ray ID: ${cfRay}`,
        { status: 500, headers: { "Content-Type": "text/html;charset=UTF-8" } }
      );
    }
    waitUntil(
      logVisit(env, {
        timestamp,
        cfRay,
        offerId: OFFER_ID_LOG,
        variantShown: variantDecisionForLog,
        countryCode,
        clientIp,
        clientTrustCategory,
        asOrganization,
        deviceType: uaInfo.device.type,
        isWebview: uaInfo.browser.isWebview,
        webviewAppGuess: uaInfo.browser.webviewAppName,
        osName: uaInfo.os.name,
        browserName: uaInfo.browser.name,
        userAgentRaw,
        allHeadersRaw,
        cfObjectRaw,
        requestUrl,
        filterPassedReason
      })
    );
    return responseToClient;
  } else {
    console.log(`[${request.headers.get("cf-ray") || "NO-RAY"}] Passing through request for path: ${path} (URL: ${request.url}) to be handled by Pages.`);
    return await next();
  }
}
__name(onRequest, "onRequest");
async function logVisit(env, data) {
  try {
    const stmt = env.USER_TRACKING_DB.prepare(
      `INSERT INTO user_visits_log (
                timestamp, cf_ray, offer_id, variant_shown, country_code, client_ip, 
                client_trust_category, as_organization, device_type, is_webview, webview_app_guess, 
                os_name, browser_name, user_agent_raw, all_headers_raw, cf_object_raw, 
                request_url, filter_passed_reason
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
    );
    await stmt.bind(
      data.timestamp,
      data.cfRay,
      data.offerId,
      data.variantShown,
      data.countryCode,
      data.clientIp,
      data.clientTrustCategory,
      data.asOrganization,
      data.deviceType,
      data.isWebview,
      data.webviewAppGuess,
      data.osName,
      data.browserName,
      data.userAgentRaw,
      data.allHeadersRaw,
      data.cfObjectRaw,
      data.requestUrl,
      data.filterPassedReason
    ).run();
  } catch (dbError) {
    console.error(`[${data.cfRay}] D1 Insert Error: ${dbError.message}`, dbError.cause ? dbError.cause.message : "", dbError.stack);
  }
}
__name(logVisit, "logVisit");
function parseUserAgent(ua) {
  const result = {
    browser: { name: "unknown", version: "unknown", isWebview: false, webviewAppName: "N/A", isFacebookApp: false },
    os: { name: "unknown", version: "unknown" },
    device: { type: "unknown", vendor: "unknown", model: "unknown" }
  };
  if (!ua || typeof ua !== "string")
    return result;
  if (/FBAN|FBAV|FB_IAB|FB4A|FBPN\/graph\.facebook\.katana|FBOP\/1|FBSN\/|FBMS\/|Messenger|Instagram|FBMF\/|FBBR\/|FBBD\/|FBBV\/|FBSV\/|FBCR\/|FBDM\/|FBBLK\/|FBLC\/|FBES\/|FBMA\/|FBCA\/|FBCT\/|FBCN\/|FBIOS\b/i.test(ua)) {
    result.browser.isWebview = true;
    result.browser.isFacebookApp = true;
    if (/Messenger|Orca-Android|FBAN\/MessengerForiOS/i.test(ua)) {
      result.browser.webviewAppName = "Facebook Messenger";
    } else if (/Instagram/i.test(ua)) {
      result.browser.webviewAppName = "Instagram";
    } else {
      result.browser.webviewAppName = "Facebook";
    }
    const fbavMatch = ua.match(/FBAV\/([\d\.]+)/i);
    if (fbavMatch)
      result.browser.version = fbavMatch[1];
  }
  if (!result.browser.isFacebookApp && /(; wv\)|Mobile\).*wv|WebView|Crosswalk)/i.test(ua)) {
    result.browser.isWebview = true;
    if (result.browser.webviewAppName === "N/A") {
      result.browser.webviewAppName = "Android System WebView";
    }
  }
  if (/GSA\/([\d\.]+)/i.test(ua)) {
    result.browser.isWebview = true;
    result.browser.webviewAppName = "Google Search App";
    result.browser.name = "Google Search App";
    result.browser.version = RegExp.$1;
  }
  if (/Windows NT 10\.0/i.test(ua)) {
    result.os.name = "Windows";
    result.os.version = "10";
  } else if (/Windows NT 6\.3/i.test(ua)) {
    result.os.name = "Windows";
    result.os.version = "8.1";
  } else if (/Windows NT 6\.2/i.test(ua)) {
    result.os.name = "Windows";
    result.os.version = "8";
  } else if (/Windows NT 6\.1/i.test(ua)) {
    result.os.name = "Windows";
    result.os.version = "7";
  } else if (/Windows Phone (?:OS )?([\d\.]+)/i.test(ua)) {
    result.os.name = "Windows Phone";
    result.os.version = RegExp.$1;
  } else if (/Android(?:[\s\/]([\d\.]+))?/i.test(ua)) {
    result.os.name = "Android";
    if (RegExp.$1)
      result.os.version = RegExp.$1;
  } else if (/(iPhone|iPad|iPod)(?:.*OS ([\d_]+))?/i.test(ua)) {
    result.os.name = "iOS";
    if (RegExp.$2)
      result.os.version = RegExp.$2.replace(/_/g, ".");
    result.device.vendor = "Apple";
    result.device.model = RegExp.$1;
  } else if (/(Mac OS X |Macintosh;.* OS X )([\d_]+)/i.test(ua)) {
    result.os.name = "macOS";
    result.os.version = RegExp.$2.replace(/_/g, ".");
  } else if (/CrOS/i.test(ua)) {
    result.os.name = "Chrome OS";
  } else if (/Linux/i.test(ua) && !/Android/i.test(ua)) {
    result.os.name = "Linux";
  }
  if (result.browser.name === "unknown" || result.browser.isWebview && result.browser.webviewAppName === "Android System WebView" || result.browser.isWebview && result.browser.webviewAppName === "N/A" && result.os.name === "iOS") {
    if (/(?:Edg|Edge|EdgA|EdgiOS)\/([\d\.]+)/i.test(ua)) {
      result.browser.name = "Edge";
      result.browser.version = RegExp.$1;
    } else if (/(?:OPR|Opera)[\s\/]([\d\.]+)/i.test(ua)) {
      result.browser.name = "Opera";
      result.browser.version = RegExp.$1;
    } else if (/(?:Chrome|CriOS|CrMo)\/([\d\.]+)/i.test(ua)) {
      result.browser.name = "Chrome";
      result.browser.version = RegExp.$1;
    } else if (/(?:Firefox|FxiOS|Focus)\/([\d\.]+)/i.test(ua)) {
      result.browser.name = "Firefox";
      result.browser.version = RegExp.$1;
    } else if (/MSIE ([\d\.]+)/i.test(ua) || /Trident\/.*; rv:([\d\.]+)/i.test(ua)) {
      result.browser.name = "Internet Explorer";
      result.browser.version = RegExp.$1 || RegExp.$2;
    } else if (/Version\/([\d\.]+).*Safari\//i.test(ua)) {
      result.browser.name = "Safari";
      result.browser.version = RegExp.$1;
    }
    if (result.os.name === "Android" && result.browser.name === "Chrome" && /\bwv\b/.test(ua)) {
      result.browser.isWebview = true;
      result.browser.webviewAppName = "Android System WebView";
    }
    if (result.os.name === "iOS" && !result.browser.isFacebookApp && !/(CriOS|FxiOS|EdgiOS|OPiOS|Focus|GSA)/i.test(ua)) {
      if (result.browser.name !== "Safari" && result.browser.name !== "unknown") {
        result.browser.isWebview = true;
        if (result.browser.webviewAppName === "N/A")
          result.browser.webviewAppName = `${result.browser.name} (WebView)`;
      }
    }
  }
  if (/Mobi/i.test(ua) || result.os.name === "Android" || result.os.name === "iOS" || result.os.name === "Windows Phone") {
    if (/Tablet|iPad|PlayBook/i.test(ua) || result.os.name === "Android" && !/Mobile/i.test(ua)) {
      result.device.type = "tablet";
    } else {
      result.device.type = "mobile";
    }
  } else if (result.os.name === "Windows" || result.os.name === "macOS" || result.os.name === "Linux" || result.os.name === "Chrome OS") {
    result.device.type = "desktop";
  }
  if (result.device.model === "iPad")
    result.device.type = "tablet";
  if (result.browser.webviewAppName !== "N/A" && result.browser.webviewAppName !== "Android System WebView" && !result.browser.isWebview) {
    result.browser.isWebview = true;
  }
  if (result.browser.isFacebookApp && !result.browser.isWebview) {
    result.browser.isWebview = true;
  }
  return result;
}
__name(parseUserAgent, "parseUserAgent");

// ../.wrangler/tmp/pages-Zlnn7D/functionsRoutes-0.6394216696895185.mjs
var routes = [
  {
    routePath: "/",
    mountPath: "/",
    method: "",
    middlewares: [onRequest],
    modules: []
  }
];

// ../node_modules/path-to-regexp/dist.es2015/index.js
function lexer(str) {
  var tokens = [];
  var i = 0;
  while (i < str.length) {
    var char = str[i];
    if (char === "*" || char === "+" || char === "?") {
      tokens.push({ type: "MODIFIER", index: i, value: str[i++] });
      continue;
    }
    if (char === "\\") {
      tokens.push({ type: "ESCAPED_CHAR", index: i++, value: str[i++] });
      continue;
    }
    if (char === "{") {
      tokens.push({ type: "OPEN", index: i, value: str[i++] });
      continue;
    }
    if (char === "}") {
      tokens.push({ type: "CLOSE", index: i, value: str[i++] });
      continue;
    }
    if (char === ":") {
      var name = "";
      var j = i + 1;
      while (j < str.length) {
        var code = str.charCodeAt(j);
        if (
          // `0-9`
          code >= 48 && code <= 57 || // `A-Z`
          code >= 65 && code <= 90 || // `a-z`
          code >= 97 && code <= 122 || // `_`
          code === 95
        ) {
          name += str[j++];
          continue;
        }
        break;
      }
      if (!name)
        throw new TypeError("Missing parameter name at ".concat(i));
      tokens.push({ type: "NAME", index: i, value: name });
      i = j;
      continue;
    }
    if (char === "(") {
      var count = 1;
      var pattern = "";
      var j = i + 1;
      if (str[j] === "?") {
        throw new TypeError('Pattern cannot start with "?" at '.concat(j));
      }
      while (j < str.length) {
        if (str[j] === "\\") {
          pattern += str[j++] + str[j++];
          continue;
        }
        if (str[j] === ")") {
          count--;
          if (count === 0) {
            j++;
            break;
          }
        } else if (str[j] === "(") {
          count++;
          if (str[j + 1] !== "?") {
            throw new TypeError("Capturing groups are not allowed at ".concat(j));
          }
        }
        pattern += str[j++];
      }
      if (count)
        throw new TypeError("Unbalanced pattern at ".concat(i));
      if (!pattern)
        throw new TypeError("Missing pattern at ".concat(i));
      tokens.push({ type: "PATTERN", index: i, value: pattern });
      i = j;
      continue;
    }
    tokens.push({ type: "CHAR", index: i, value: str[i++] });
  }
  tokens.push({ type: "END", index: i, value: "" });
  return tokens;
}
__name(lexer, "lexer");
function parse(str, options) {
  if (options === void 0) {
    options = {};
  }
  var tokens = lexer(str);
  var _a = options.prefixes, prefixes = _a === void 0 ? "./" : _a, _b = options.delimiter, delimiter = _b === void 0 ? "/#?" : _b;
  var result = [];
  var key = 0;
  var i = 0;
  var path = "";
  var tryConsume = /* @__PURE__ */ __name(function(type) {
    if (i < tokens.length && tokens[i].type === type)
      return tokens[i++].value;
  }, "tryConsume");
  var mustConsume = /* @__PURE__ */ __name(function(type) {
    var value2 = tryConsume(type);
    if (value2 !== void 0)
      return value2;
    var _a2 = tokens[i], nextType = _a2.type, index = _a2.index;
    throw new TypeError("Unexpected ".concat(nextType, " at ").concat(index, ", expected ").concat(type));
  }, "mustConsume");
  var consumeText = /* @__PURE__ */ __name(function() {
    var result2 = "";
    var value2;
    while (value2 = tryConsume("CHAR") || tryConsume("ESCAPED_CHAR")) {
      result2 += value2;
    }
    return result2;
  }, "consumeText");
  var isSafe = /* @__PURE__ */ __name(function(value2) {
    for (var _i = 0, delimiter_1 = delimiter; _i < delimiter_1.length; _i++) {
      var char2 = delimiter_1[_i];
      if (value2.indexOf(char2) > -1)
        return true;
    }
    return false;
  }, "isSafe");
  var safePattern = /* @__PURE__ */ __name(function(prefix2) {
    var prev = result[result.length - 1];
    var prevText = prefix2 || (prev && typeof prev === "string" ? prev : "");
    if (prev && !prevText) {
      throw new TypeError('Must have text between two parameters, missing text after "'.concat(prev.name, '"'));
    }
    if (!prevText || isSafe(prevText))
      return "[^".concat(escapeString(delimiter), "]+?");
    return "(?:(?!".concat(escapeString(prevText), ")[^").concat(escapeString(delimiter), "])+?");
  }, "safePattern");
  while (i < tokens.length) {
    var char = tryConsume("CHAR");
    var name = tryConsume("NAME");
    var pattern = tryConsume("PATTERN");
    if (name || pattern) {
      var prefix = char || "";
      if (prefixes.indexOf(prefix) === -1) {
        path += prefix;
        prefix = "";
      }
      if (path) {
        result.push(path);
        path = "";
      }
      result.push({
        name: name || key++,
        prefix,
        suffix: "",
        pattern: pattern || safePattern(prefix),
        modifier: tryConsume("MODIFIER") || ""
      });
      continue;
    }
    var value = char || tryConsume("ESCAPED_CHAR");
    if (value) {
      path += value;
      continue;
    }
    if (path) {
      result.push(path);
      path = "";
    }
    var open = tryConsume("OPEN");
    if (open) {
      var prefix = consumeText();
      var name_1 = tryConsume("NAME") || "";
      var pattern_1 = tryConsume("PATTERN") || "";
      var suffix = consumeText();
      mustConsume("CLOSE");
      result.push({
        name: name_1 || (pattern_1 ? key++ : ""),
        pattern: name_1 && !pattern_1 ? safePattern(prefix) : pattern_1,
        prefix,
        suffix,
        modifier: tryConsume("MODIFIER") || ""
      });
      continue;
    }
    mustConsume("END");
  }
  return result;
}
__name(parse, "parse");
function match(str, options) {
  var keys = [];
  var re = pathToRegexp(str, keys, options);
  return regexpToFunction(re, keys, options);
}
__name(match, "match");
function regexpToFunction(re, keys, options) {
  if (options === void 0) {
    options = {};
  }
  var _a = options.decode, decode = _a === void 0 ? function(x) {
    return x;
  } : _a;
  return function(pathname) {
    var m = re.exec(pathname);
    if (!m)
      return false;
    var path = m[0], index = m.index;
    var params = /* @__PURE__ */ Object.create(null);
    var _loop_1 = /* @__PURE__ */ __name(function(i2) {
      if (m[i2] === void 0)
        return "continue";
      var key = keys[i2 - 1];
      if (key.modifier === "*" || key.modifier === "+") {
        params[key.name] = m[i2].split(key.prefix + key.suffix).map(function(value) {
          return decode(value, key);
        });
      } else {
        params[key.name] = decode(m[i2], key);
      }
    }, "_loop_1");
    for (var i = 1; i < m.length; i++) {
      _loop_1(i);
    }
    return { path, index, params };
  };
}
__name(regexpToFunction, "regexpToFunction");
function escapeString(str) {
  return str.replace(/([.+*?=^!:${}()[\]|/\\])/g, "\\$1");
}
__name(escapeString, "escapeString");
function flags(options) {
  return options && options.sensitive ? "" : "i";
}
__name(flags, "flags");
function regexpToRegexp(path, keys) {
  if (!keys)
    return path;
  var groupsRegex = /\((?:\?<(.*?)>)?(?!\?)/g;
  var index = 0;
  var execResult = groupsRegex.exec(path.source);
  while (execResult) {
    keys.push({
      // Use parenthesized substring match if available, index otherwise
      name: execResult[1] || index++,
      prefix: "",
      suffix: "",
      modifier: "",
      pattern: ""
    });
    execResult = groupsRegex.exec(path.source);
  }
  return path;
}
__name(regexpToRegexp, "regexpToRegexp");
function arrayToRegexp(paths, keys, options) {
  var parts = paths.map(function(path) {
    return pathToRegexp(path, keys, options).source;
  });
  return new RegExp("(?:".concat(parts.join("|"), ")"), flags(options));
}
__name(arrayToRegexp, "arrayToRegexp");
function stringToRegexp(path, keys, options) {
  return tokensToRegexp(parse(path, options), keys, options);
}
__name(stringToRegexp, "stringToRegexp");
function tokensToRegexp(tokens, keys, options) {
  if (options === void 0) {
    options = {};
  }
  var _a = options.strict, strict = _a === void 0 ? false : _a, _b = options.start, start = _b === void 0 ? true : _b, _c = options.end, end = _c === void 0 ? true : _c, _d = options.encode, encode = _d === void 0 ? function(x) {
    return x;
  } : _d, _e = options.delimiter, delimiter = _e === void 0 ? "/#?" : _e, _f = options.endsWith, endsWith = _f === void 0 ? "" : _f;
  var endsWithRe = "[".concat(escapeString(endsWith), "]|$");
  var delimiterRe = "[".concat(escapeString(delimiter), "]");
  var route = start ? "^" : "";
  for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {
    var token = tokens_1[_i];
    if (typeof token === "string") {
      route += escapeString(encode(token));
    } else {
      var prefix = escapeString(encode(token.prefix));
      var suffix = escapeString(encode(token.suffix));
      if (token.pattern) {
        if (keys)
          keys.push(token);
        if (prefix || suffix) {
          if (token.modifier === "+" || token.modifier === "*") {
            var mod = token.modifier === "*" ? "?" : "";
            route += "(?:".concat(prefix, "((?:").concat(token.pattern, ")(?:").concat(suffix).concat(prefix, "(?:").concat(token.pattern, "))*)").concat(suffix, ")").concat(mod);
          } else {
            route += "(?:".concat(prefix, "(").concat(token.pattern, ")").concat(suffix, ")").concat(token.modifier);
          }
        } else {
          if (token.modifier === "+" || token.modifier === "*") {
            throw new TypeError('Can not repeat "'.concat(token.name, '" without a prefix and suffix'));
          }
          route += "(".concat(token.pattern, ")").concat(token.modifier);
        }
      } else {
        route += "(?:".concat(prefix).concat(suffix, ")").concat(token.modifier);
      }
    }
  }
  if (end) {
    if (!strict)
      route += "".concat(delimiterRe, "?");
    route += !options.endsWith ? "$" : "(?=".concat(endsWithRe, ")");
  } else {
    var endToken = tokens[tokens.length - 1];
    var isEndDelimited = typeof endToken === "string" ? delimiterRe.indexOf(endToken[endToken.length - 1]) > -1 : endToken === void 0;
    if (!strict) {
      route += "(?:".concat(delimiterRe, "(?=").concat(endsWithRe, "))?");
    }
    if (!isEndDelimited) {
      route += "(?=".concat(delimiterRe, "|").concat(endsWithRe, ")");
    }
  }
  return new RegExp(route, flags(options));
}
__name(tokensToRegexp, "tokensToRegexp");
function pathToRegexp(path, keys, options) {
  if (path instanceof RegExp)
    return regexpToRegexp(path, keys);
  if (Array.isArray(path))
    return arrayToRegexp(path, keys, options);
  return stringToRegexp(path, keys, options);
}
__name(pathToRegexp, "pathToRegexp");

// ../node_modules/wrangler/templates/pages-template-worker.ts
var escapeRegex = /[.+?^${}()|[\]\\]/g;
function* executeRequest(request) {
  const requestPath = new URL(request.url).pathname;
  for (const route of [...routes].reverse()) {
    if (route.method && route.method !== request.method) {
      continue;
    }
    const routeMatcher = match(route.routePath.replace(escapeRegex, "\\$&"), {
      end: false
    });
    const mountMatcher = match(route.mountPath.replace(escapeRegex, "\\$&"), {
      end: false
    });
    const matchResult = routeMatcher(requestPath);
    const mountMatchResult = mountMatcher(requestPath);
    if (matchResult && mountMatchResult) {
      for (const handler of route.middlewares.flat()) {
        yield {
          handler,
          params: matchResult.params,
          path: mountMatchResult.path
        };
      }
    }
  }
  for (const route of routes) {
    if (route.method && route.method !== request.method) {
      continue;
    }
    const routeMatcher = match(route.routePath.replace(escapeRegex, "\\$&"), {
      end: true
    });
    const mountMatcher = match(route.mountPath.replace(escapeRegex, "\\$&"), {
      end: false
    });
    const matchResult = routeMatcher(requestPath);
    const mountMatchResult = mountMatcher(requestPath);
    if (matchResult && mountMatchResult && route.modules.length) {
      for (const handler of route.modules.flat()) {
        yield {
          handler,
          params: matchResult.params,
          path: matchResult.path
        };
      }
      break;
    }
  }
}
__name(executeRequest, "executeRequest");
var pages_template_worker_default = {
  async fetch(originalRequest, env, workerContext) {
    let request = originalRequest;
    const handlerIterator = executeRequest(request);
    let data = {};
    let isFailOpen = false;
    const next = /* @__PURE__ */ __name(async (input, init) => {
      if (input !== void 0) {
        let url = input;
        if (typeof input === "string") {
          url = new URL(input, request.url).toString();
        }
        request = new Request(url, init);
      }
      const result = handlerIterator.next();
      if (result.done === false) {
        const { handler, params, path } = result.value;
        const context = {
          request: new Request(request.clone()),
          functionPath: path,
          next,
          params,
          get data() {
            return data;
          },
          set data(value) {
            if (typeof value !== "object" || value === null) {
              throw new Error("context.data must be an object");
            }
            data = value;
          },
          env,
          waitUntil: workerContext.waitUntil.bind(workerContext),
          passThroughOnException: () => {
            isFailOpen = true;
          }
        };
        const response = await handler(context);
        if (!(response instanceof Response)) {
          throw new Error("Your Pages function should return a Response");
        }
        return cloneResponse(response);
      } else if ("ASSETS") {
        const response = await env["ASSETS"].fetch(request);
        return cloneResponse(response);
      } else {
        const response = await fetch(request);
        return cloneResponse(response);
      }
    }, "next");
    try {
      return await next();
    } catch (error) {
      if (isFailOpen) {
        const response = await env["ASSETS"].fetch(request);
        return cloneResponse(response);
      }
      throw error;
    }
  }
};
var cloneResponse = /* @__PURE__ */ __name((response) => (
  // https://fetch.spec.whatwg.org/#null-body-status
  new Response(
    [101, 204, 205, 304].includes(response.status) ? null : response.body,
    response
  )
), "cloneResponse");
export {
  pages_template_worker_default as default
};
