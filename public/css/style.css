@keyframes rotation-loader{0%{transform:rotate(0deg)}to{transform:rotate(360deg)}}@-webkit-keyframes spinTo1{0%{-webkit-transform:rotate(-44deg);transform:rotate(-44deg)}to{transform:rotate(1080deg)}}@-webkit-keyframes spinTo2{0%{transform:rotate(-4deg)}to{transform:rotate(1396deg)}}@-webkit-keyframes spinTo3{0%{transform:rotate(-4deg)}to{transform:rotate(1351deg)}}@-webkit-keyframes spinTo4{0%{transform:rotate(-4deg)}to{transform:rotate(1305deg)}}@-webkit-keyframes spinTo5{0%{transform:rotate(-4deg)}to{transform:rotate(1261deg)}}@-webkit-keyframes spinTo6{0%{transform:rotate(-4deg)}to{transform:rotate(1217deg)}}@-webkit-keyframes spinTo7{0%{transform:rotate(-4deg)}to{transform:rotate(1531deg)}}@-webkit-keyframes spinTo8{0%{transform:rotate(-4deg)}to{transform:rotate(1485deg)}}@-webkit-keyframes spinner-win{0%,to{transform:rotate(1080deg)}50%{transform:rotate(1085deg)}}@keyframes moveField1{0%,20%{top:-32em}10%{top:-28em}to{top:0}}@keyframes moveField2{0%,20%{top:-25em}10%{top:-20em}to{top:0}}@keyframes moveField3{0%,20%{top:-31em}10%{top:-26em}to{top:0}}@keyframes moveField4{0%,20%{top:-25.5em}10%{top:-20.5em}to{top:0}}@keyframes moveLine{0%{transform:translateX(0)}to{transform:translateX(50%)}}@keyframes moveLineMob{0%{transform:translateX(0)}to{transform:translateX(33.3%)}}@keyframes rotateSun{0%{transform:rotate(0deg)}to{transform:rotate(360deg)}}@keyframes moveCloud1{0%,to{transform:translateX(0)}50%{transform:translateX(-50vw)}}@keyframes moveCloud2{0%,to{transform:translateX(-50vw)}50%{transform:translateX(0)}}@keyframes scaleMultiplier{0%{transform:scale(0)}to{transform:scale(1)}}@keyframes moveHook{0%,to{transform:translate(-15em,0)}25%,75%{transform:translate(0,5em)}50%{transform:translate(15em,0)}}@keyframes moveBuild{0%,to{transform:translate(-15em,0)}25%,75%{transform:translate(0,5em)}50%{transform:translate(15em,0)}}@keyframes stableBuild{to{transform:rotate(0deg) translate(0,0)}}@keyframes rotateBuild{0%,60%,to{transform:rotate(-15deg) translate(5.5em,.8em)}10%,50%{transform:rotate(15deg) translate(-4.8em,.8em)}}@-webkit-keyframes scaleImg{0%,to{transform:scale(1) translateY(0)}50%{transform:scale(1.2) translateY(-1em)}}*{margin:0;padding:0}*,::after,::before{box-sizing:border-box}ol[role=list],ul[role=list]{list-style:none}html:focus-within{scroll-behavior:smooth}a{text-decoration:none!important}a:not([class]){text-decoration-skip-ink:auto}canvas,img,picture,svg,video{max-width:100%;height:auto;vertical-align:middle;font-style:italic;background-repeat:no-repeat;background-size:cover}button,input,select,textarea{font:inherit}@media (prefers-reduced-motion:reduce){html:focus-within{scroll-behavior:auto}*,::after,::before{animation-duration:.01ms!important;animation-iteration-count:1!important;transition-duration:.01ms!important;scroll-behavior:auto!important;transition:none}}body,html{height:100%;scroll-behavior:smooth}.hidden{display:none!important}.visible{display:flex!important}body{user-select:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;overflow-x:hidden;overflow-y:auto;font-size:min(4.5px + 5.5*(100vw - 375px)/1545,10px);font-style:normal;font-size:90%}.bottom__section-button{min-width:50px;align-items:center}.bottom__section-button span{height:auto}.bottom__section-button .button-loader,.game.is--step-2 .game__field-tower-item.is--1 .game__field-tower-item-build::before,.game.is--step-3 .game__field-tower-item.is--2 .game__field-tower-item-build::before,.game.is--step-4 .game__field-tower-item.is--3 .game__field-tower-item-build::before,.game.is--win .game__field-tower-item.is--4 .game__field-tower-item-build::before{display:none}.bottom__section-button.is--link{display:flex;align-items:center;justify-content:center}.bottom__section-button.is--disabled{pointer-events:none}.bottom__section-button.is--disabled span{opacity:.2}.bottom__section-button.is--disabled .button-loader{display:flex}.button-loader{position:absolute;align-items:center;justify-content:center;left:0;top:0;width:100%;height:100%}.button-loader span{width:30px!important;height:30px!important;border:4px solid #fff;border-bottom-color:transparent;border-radius:50%;display:inline-block;box-sizing:border-box;animation:rotation-loader 1s linear infinite;opacity:1!important;margin-left:0!important;margin-right:0!important}.wheel__spinner_win_1{animation:3s spinTo1 ease-in-out forwards!important}.wheel__spinner_win_2{animation:3s spinTo2 ease-in-out forwards}.wheel__spinner_win_3{animation:3s spinTo3 ease-in-out forwards}.wheel__spinner_win_4{animation:3s spinTo4 ease-in-out forwards}.wheel__spinner_win_5{animation:3s spinTo5 ease-in-out forwards}.wheel__spinner_win_6{animation:3s spinTo6 ease-in-out forwards}.wheel__spinner_win_7{animation:3s spinTo7 ease-in-out forwards}.wheel__spinner_win_8{animation:3s spinTo8 ease-in-out forwards}.wheel__texts-block{display:flex;flex-direction:column;justify-content:center;align-items:center;padding-right:1em!important;padding-left:2em!important}.wheel__texts-block p{transform:rotate(-7deg)}.wheel__texts-block p:only-child{transform:rotate(0deg)}.wheel__texts-block p+p{margin-top:10px;transform:rotate(7deg)}.effects{position:absolute;left:0;top:0;width:100%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;mix-blend-mode:screen}.effects__block,.game__controls::before{top:0;left:0;width:100%;background-size:cover}.effects__block{background-repeat:no-repeat;-webkit-background-size:cover;background-position:center;position:fixed;height:100%}.wheel__texts-1,.wheel__texts-2{transform:rotate(-137deg);position:absolute;left:11%;top:23%}.wheel__texts-2{transform:rotate(-93deg);left:32%;top:14%}.wheel__texts-3,.wheel__texts-4{transform:rotate(-45deg);position:absolute;left:54%;top:23%}.wheel__texts-4{transform:rotate(-2deg);left:63%;top:44%}.wheel__texts-5,.wheel__texts-6{transform:rotate(43deg);position:absolute;left:54%;top:65%}.wheel__texts-6{transform:rotate(87deg);left:33%;top:74%}.wheel__texts-7,.wheel__texts-8{transform:rotate(133deg);position:absolute;left:11%;top:66%}.wheel__texts-8{transform:rotate(177deg);left:2%;top:45%}.win-animation-svg{position:absolute;left:0;top:0;transform:rotate(45deg);opacity:0;width:100%}.win-animation-svg.is--active{opacity:1}.is--win-spinner{animation:2s spinner-win ease-in-out infinite!important}body.is--winner .bottom__section{opacity:0}html{background:#314353;font-size:min(4.5px + 5.5*(100vw - 375px)/1545,10px)}@media (max-width:1440px){body{font-size:80%}}@media (max-width:768px){body{font-size:100%}}img{width:100%}.body-wrapper{display:flex;position:relative;width:100%;min-height:100vh;overflow:hidden;background-repeat:no-repeat;background-position:center;background-size:cover;background-image:url(../images/sky.webp)}@media (max-width:767px){.body-wrapper{background-image:url(../images/sky-mob.webp)}}.logo{position:fixed;z-index:5;top:6em;left:9em;width:28.7em;height:17.2em}@media (max-width:767px){.logo{left:5em;top:6em}}.shield{position:fixed;z-index:5;top:8em;right:11.3em;width:9.6em;height:9.6em}@media (max-width:767px){.shield{font-size:140%;right:4em;top:5em}}.game.is--started .game__controls-cash{opacity:1;pointer-events:all}.game.is--started .game__field-results{display:flex}.game.is--multiplier .game__field-multiplier{display:block}.game.is--active .game__controls-cash,.game.is--active .game__controls-go .bottom__section-button{opacity:.5;pointer-events:none}.game.is--active .game__field-tower-hook{top:-25em}.game.is--step-1.is--active .game__field-tower-item.is--1,.game.is--step-1.is--active .game__field-tower-item.is--1 .game__field-tower-item-build,.game.is--step-2.is--active .game__field-tower-item.is--2,.game.is--step-2.is--active .game__field-tower-item.is--2 .game__field-tower-item-build,.game.is--step-3.is--active .game__field-tower-item.is--3,.game.is--step-3.is--active .game__field-tower-item.is--3 .game__field-tower-item-build,.game.is--step-4.is--active .game__field-tower-item.is--4,.game.is--step-4.is--active .game__field-tower-item.is--4 .game__field-tower-item-build{animation-play-state:paused}.game.is--step-1 .game__field-tower-item.is--1,.game.is--step-2 .game__field-tower-item.is--2,.game.is--step-3 .game__field-tower-item.is--3,.game.is--step-4 .game__field-tower-item.is--4{opacity:1}.game.is--step-2 .game__field{height:calc(100% + 32em);animation:.3s moveField1 linear forwards}.game.is--step-3 .game__field{height:calc(100% + 57em);animation:.3s moveField2 linear forwards}.game.is--step-4 .game__field{height:calc(100% + 88em);animation:.3s moveField3 linear forwards}.game.is--win .game__controls-cash,.game.is--win .game__controls-go .bottom__section-button{opacity:.5;pointer-events:none}.game.is--win .game__field-tower-hook{top:-30em}.game.is--win .game__field{height:calc(100% + 113.5em);animation:.3s moveField4 linear forwards}.game__controls{z-index:5;position:fixed;left:50%;bottom:0;display:flex;align-items:center;justify-content:space-between;gap:1.2em;width:92em;height:20em;padding:2.1em;border-radius:6px 6px 0 0;overflow:hidden;transform:translateX(-50%);background:linear-gradient(180deg,#2b2b2b 0,#121212 100%)}@media (max-width:767px){.game__controls{flex-direction:column;width:100%;height:38em;gap:2em;padding:3em;font-size:140%;border-radius:0}}.game__controls::before{content:"";display:block;position:absolute;height:5px;background-position:top;background-repeat:repeat-x;background-image:url(../images/line.webp)}.game__controls-cash{display:flex;align-items:center;justify-content:center;flex-direction:column;width:43.4em;height:14.8em;border:0;background-size:contain;background-position:center;background-repeat:no-repeat;background-color:transparent;background-image:url(../images/button-blue.webp);opacity:.5;cursor:pointer;pointer-events:none}@media (max-width:767px){.game__controls-cash{width:55em;background-image:url(../images/button-blue-mob.webp)}}.game__controls-cash-label{display:block;color:#fff;text-align:center;font-size:3.6em;letter-spacing:.07em;text-shadow:0 .11em .27em rgba(0,0,0,.45)}.game__controls-cash-value{display:block;margin-top:.4em;color:#fff;text-align:center;font-size:2.5em}.game__controls-go .bottom__section-button,.game__controls-go .bottom__section-button::before{overflow:hidden;background-size:contain;background-position:center;background-color:transparent}.game__controls-go .bottom__section-button{position:relative;display:flex;align-items:center;justify-content:center;flex-direction:column;width:43.4em;height:14.8em;border:0;border-radius:1em;background-repeat:no-repeat;background-image:url(../images/button-yellow.webp);cursor:pointer;outline:0;text-decoration:none;-webkit-tap-highlight-color:transparent}@media (max-width:767px){.game__controls-go .bottom__section-button{width:55em;background-image:url(../images/button-yellow-mob.webp)}}.game__controls-go .bottom__section-button::before{content:"";display:block;position:absolute;left:-43.2em;top:calc(50% - 6.75em);width:86.4em;height:13.5em;border-radius:1.4em;background-repeat:repeat-x;background-image:url(../images/button-lines.svg);animation:7s moveLine linear infinite}@media (max-width:767px){.game__controls-go .bottom__section-button::before{width:129.6em;animation:7s moveLineMob linear infinite}}.game__controls-go .bottom__section-button-default,.modal__win-btn .bottom__section-button span{display:block;color:#fff;text-align:center;font-size:3.6em;letter-spacing:.07em;text-shadow:0 .11em .27em rgba(0,0,0,.45)}.game__controls-go .bottom__section-button-next{display:none}.game__field,.game__field-street{position:absolute;left:0;width:100%}.game__field{bottom:0;height:100vh;display:flex;align-items:center;justify-content:center}.game__field::after,.game__field::before{content:"";display:block;position:absolute;bottom:0;background-repeat:no-repeat;background-position:center;background-size:cover;width:calc(50% - 45em);z-index:0;height:20em}@media (max-width:767px){.game__field::after,.game__field::before{display:none}}.game__field::before{left:0;background-image:url(../images/ground-left.webp)}.game__field::after{right:0;background-image:url(../images/ground-right.webp)}.game__field-street{bottom:19.5em;height:100em}@media (max-width:767px){.game__field-street{bottom:52.5em}}.game__field-street::before{background-size:contain}.game__field-street::after,.game__field-street::before{content:"";display:block;position:absolute;background-repeat:no-repeat;background-position:bottom}.game__field-street::before{left:calc(50% - 27em);bottom:0;width:54em;height:54em;background-image:url(../images/sun.webp);animation:10s rotateSun linear infinite}.game__field-street::after{left:0;top:0;width:100%;height:100%;background-size:192em;font-size:125%;background-image:url(../images/street.webp)}@media (max-width:767px){.game__field-street::after{font-size:120%}}.game__field-cloud::after,.game__field-cloud::before{content:"";display:block;position:absolute;width:51em;height:15em;background-repeat:no-repeat;background-position:center;background-size:contain;background-image:url(../images/cloud.webp)}.game__field-cloud::before{left:50%;bottom:90em;animation:25s moveCloud1 ease-in-out infinite}@media (max-width:767px){.game__field-cloud::before{bottom:120em}}.game__field-cloud::after{right:50%;bottom:150em;animation:25s moveCloud2 ease-in-out infinite}@media (max-width:767px){.game__field-cloud::after{bottom:220em}}.game__field-results{position:fixed;z-index:3;right:11em;top:25em;display:none;flex-direction:column;gap:2em}@media (max-width:767px){.game__field-results{top:unset;bottom:70em;height:30em;right:5em}}.game__field-results span,.game__field-results-label{color:#fff;text-align:center;font-size:3.2em}.game__field-results span{display:flex;width:3.75em;padding:.25em .5em;justify-content:center;align-items:center;border-radius:.25em;border:.1em solid #f7d053;background:#8b9b8d}.game__field-multiplier{display:none;position:fixed;bottom:5em;left:calc(50% - 1em);width:2em;color:#e8a81c;text-align:center;text-shadow:0 .02em .06em #fff;-webkit-text-stroke-width:-.01em;-webkit-text-stroke-color:#fff;font-size:12em;letter-spacing:-.04em;white-space:nowrap;animation:.25s scaleMultiplier ease-in-out forwards}@media (max-width:767px){.game__field-multiplier{bottom:9em}}@media (max-width:767px) and (max-height:741px){.game__field-multiplier{bottom:8em}}.game__field-tower-hook,.game__field-tower-item-build::before{background-size:contain;background-repeat:no-repeat;background-position:center}.game__field-tower-hook{position:fixed;top:-8em;left:calc(50% - 2.5em);width:5em;height:17em;background-image:url(../images/hook.webp);transition:top .5s ease-in-out;animation:3s moveHook linear infinite}.game__field-tower-drop{position:absolute;left:calc(50% - 30em);width:60em;mix-blend-mode:plus-lighter;transform:translateY(2em)}.game__field-tower-item{position:absolute;transition:bottom .5s ease-in-out;animation:3s moveBuild linear infinite;pointer-events:none}.game__field-tower-item-build{animation:3s rotateBuild linear infinite}.game__field-tower-item-build::before{content:"";display:block;position:absolute;left:0;top:-5em;width:100%;height:5em;background-image:url(../images/roupe.webp)}.game__field-tower-item.is--0{width:30em;height:38.5em;bottom:19.5em;left:calc(50% - 15em);animation:none}@media (max-width:767px){.game__field-tower-item.is--0{height:37em;bottom:52.5em}}.game__field-tower-item.is--1{opacity:0;bottom:calc(100% - 8em - 37em);width:26em;height:32em;left:calc(50% - 13em)}.game__field-tower-item.is--2{opacity:0;bottom:calc(100% - 8em - 30em);width:30em;height:25em;left:calc(50% - 15em)}.game__field-tower-item.is--3{opacity:0;bottom:calc(100% - 8em - 36em);width:26em;height:31em;left:calc(50% - 13em)}.game__field-tower-item.is--4{opacity:0;bottom:calc(100% - 8em - 30.5em);width:25em;height:25.5em;left:calc(50% - 12.5em)}.modal,.modal::before{position:absolute;left:0;top:0;width:100%;height:100%}.modal{opacity:0;display:flex;align-items:center;justify-content:center;overflow:hidden;visibility:hidden}.modal::before{content:"";display:block;background:#002343;opacity:.7}.modal__content,.modal__win-btn .bottom__section-button{position:relative;display:flex;align-items:center;justify-content:center;flex-direction:column}.modal__content{min-width:70em;max-width:100em;margin:-15em auto 0;padding:3em 16px}@media (max-width:767px){.modal__content{margin-top:-25em}}.modal__content::after,.modal__content::before{z-index:0;content:"";display:block;position:absolute;width:178em;height:112em;top:calc(50% - 50em);left:calc(50% - 89em);background-position:center;background-repeat:no-repeat;background-size:contain;mix-blend-mode:plus-lighter;font-size:120%}.modal__content::before{background-image:url(../images/glare-1.webp)}.modal__content::after{background-image:url(../images/glare-2.webp)}.modal__logo{width:30em;margin:0 auto;position:relative;z-index:2}@media (max-width:767px){.modal__logo{font-size:120%}}.modal__img{position:relative;z-index:2;display:block;width:120em;margin-top:-5em;animation:2s scaleImg ease-in-out infinite}@media (max-width:767px){.modal__img{font-size:80%}}.modal__win-btn{position:relative;margin-top:-7em}.modal__win-btn::before{z-index:2;content:"";display:block;position:absolute;width:54em;height:54em;top:calc(50% - 27em);left:calc(50% - 27em);background-position:center;background-repeat:no-repeat;background-size:contain;mix-blend-mode:plus-lighter;background-image:url(../images/flare.gif);font-size:120%}.modal__win-btn .bottom__section-button{z-index:2;min-width:43.4em;width:auto;padding:0 2em;height:14.8em;border:0;overflow:hidden;border-radius:1em;background-size:cover;background-position:center;background-repeat:no-repeat;background-color:transparent;background-image:url(../images/button-blue.webp);cursor:pointer;outline:0;text-decoration:none;-webkit-tap-highlight-color:transparent}@media (max-width:767px){.modal__win-btn .bottom__section-button{min-width:55em;background-image:url(../images/button-blue-mob.webp)}}.modal__win-btn .bottom__section-button span{font-size:4.6em}.modal__win-btn .bottom__section-button-next{display:none}.modal.is--active{opacity:1;visibility:visible}.is--modal-open .game__controls,.is--modal-open .game__field-multiplier,.is--modal-open .game__field-results,.is--modal-open .game__field-tower,.is--modal-open .logo{display:none!important}.is--modal-open .game__field{height:100vh!important}.is--modal-open .game__field::after,.is--modal-open .game__field::before{display:none;content:""}.is--modal-open .game__field-street{bottom:0}.is--modal-open .shield{z-index:0}.effects{z-index:999}