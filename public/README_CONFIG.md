# Конфигурация Magic Quiz Ngeriya

## Как изменить ссылку кнопки "Claim Now"

Для изменения ссылки, которая открывается при нажатии на кнопку "Claim Now", отредактируйте файл `js/config.js`.

### Основные настройки:

1. **Ссылка кнопки**: Измените значение `claimButtonUrl`
```javascript
claimButtonUrl: 'https://your-new-link.com'
```

2. **Открытие в новой вкладке**: Настройте `openInNewTab`
```javascript
openInNewTab: true  // открывать в новой вкладке
openInNewTab: false // открывать в той же вкладке
```

3. **Отключение кнопки после клика**: Настройте `disableAfterClick`
```javascript
disableAfterClick: true  // отключать кнопку после клика
disableAfterClick: false // не отключать кнопку
```

4. **Facebook Pixel события**: Настройте отправку событий
```javascript
facebookPixel: {
  enabled: true,    // включить/выключить Facebook Pixel
  trackLead: true   // отправлять событие Lead при клике
}
```

### Пример полной конфигурации:

```javascript
window.gameConfig = {
  claimButtonUrl: 'https://vxrqzlp.bid/cl/a79f414663249fda?p1=&p2=&source=FB&site=game',
  
  facebookPixel: {
    enabled: true,
    trackLead: true
  },
  
  button: {
    openInNewTab: true,
    disableAfterClick: true
  }
};
```

### Что происходит при клике на кнопку:

1. Отправляется Facebook событие `fbq('track', 'Lead')` (если включено)
2. Кнопка отключается и меняет текст на "Processing..." (если включено)
3. Открывается указанная ссылка в новой вкладке или той же (в зависимости от настроек)

### Устранение неполадок:

- Проверьте консоль браузера на наличие ошибок
- Убедитесь, что файл `js/config.js` загружается корректно
- Проверьте, что Facebook Pixel инициализирован, если используете события

## Исправленные проблемы:

✅ Создан манифест PWA для домена https://game.mirus.help/
✅ Создан Service Worker для устранения ошибки регистрации
✅ Исправлена ошибка "e is not defined" в common.min.js
✅ Добавлена настраиваемая ссылка для кнопки "Claim Now"
✅ Добавлена отправка Facebook события fbq('track', 'Lead')
✅ Убрано искусственное замедление загрузки (было ~50 секунд, стало 2 секунды)
✅ Оптимизирована загрузка Google Fonts (не блокирует рендеринг)
✅ Ссылка открывается в той же вкладке (не в новой)
✅ Устранена проблема с бесконечной загрузкой в браузере
✅ Добавлен preload для критически важных изображений (street.webp, logo.webp и др.)
✅ Валюта изменена с USD на NGN (найра)
✅ Убрана надпись "Your current balance", оставлены только цифры
✅ Увеличен размер шрифта и улучшено центрирование баланса

## Производительность:

- Время загрузки сокращено с ~50 секунд до 2 секунд
- Google Fonts загружаются асинхронно
- Service Worker корректно кеширует только локальные ресурсы
- Добавлены fallback шрифты для быстрого отображения
- Критически важные изображения загружаются с приоритетом (preload)
