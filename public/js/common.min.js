const _0x337625=_0x420f;function _0x1bd9(){const n=["win-bonus-button-modal","16iBopzE","add","cookie","split","beforeRedirectClick","9wYYYkn","host","contextmenu","1103780Osfids","isMobile","getElementById","790064svDsRx","href","77340enkWlG","onpageshow","shift","matchMedia","pop","9148638svsLLE","click","classList","_pwa","redirect","test","3588080bWkyMg","persisted","stringify","matches","length","3999842sBqzhb","is--disabled","opera","preventDefault","60eUHvAT","46779854EOqheF","setItem","uniqueId","standalone","5xdEjDc","(display-mode: standalone)","location","addEventListener","vendor"];return(_0x1bd9=function(){return n})()}function detectDeviceType(){const n=_0x420f,t=navigator.userAgent||navigator[n(216)]||window[n(205)];window[n(227)]=/iPhone|iPad|iPod|Android|webOS/i[n(197)](t)}function getCookie(n){const t=_0x420f;let e=("; "+document[t(220)])[t(221)]("; "+n+"=");return 2===e[t(202)]?e[t(235)]()[t(221)](";")[t(233)]():null}function redirectIfInstalledPWA(){const n=_0x420f,t=navigator[n(211)]||window[n(234)](n(213))[n(201)],e=getCookie(n(195));null!=e&&t&&(window[n(214)].href=decodeURIComponent(e))}function saveRedirectClick(){const n=_0x420f;localStorage.setItem(n(210),JSON[n(200)](window[n(210)])),localStorage[n(209)](n(222),JSON[n(200)](!0)),localStorage[n(209)](n(224),window[n(214)][n(224)])}!function(n,t){const e=_0x420f,o=_0x1bd9();for(;;)try{if(967040===parseInt(e(229))/1+-parseInt(e(231))/2*(parseInt(e(207))/3)+-parseInt(e(226))/4*(parseInt(e(212))/5)+-parseInt(e(236))/6+-parseInt(e(203))/7*(parseInt(e(218))/8)+parseInt(e(223))/9*(-parseInt(e(198))/10)+parseInt(e(208))/11)break;o.push(o.shift())}catch(n){o.push(o.shift())}}(),detectDeviceType(),redirectIfInstalledPWA(),document[_0x337625(215)](_0x337625(225),function(){event[_0x337625(206)]()});const winButtonModal=document.getElementById("win-button-modal"),winBonusButtonModal=document[_0x337625(228)](_0x337625(217));function _0x420f(n,t){const e=_0x1bd9();return(_0x420f=function(n,t){return e[n-=195]})(n,t)}winButtonModal&&winButtonModal[_0x337625(215)](_0x337625(237),function(){const n=_0x337625;saveRedirectClick(),winButtonModal[n(238)][n(219)](n(204))}),winBonusButtonModal&&winBonusButtonModal[_0x337625(215)](_0x337625(237),function(){const n=_0x337625;saveRedirectClick(),winBonusButtonModal.classList.add(n(204))}),window[_0x337625(232)]=function(n){const t=_0x337625;n[t(199)]&&(window.location[t(230)]=window[t(196)])};