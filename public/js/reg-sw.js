window.addEventListener('DOMContentLoaded', async () => {
  if ('serviceWorker' in navigator) {
    try {
      const reg = await navigator.serviceWorker.register('/sw.js', { scope: './' });
      window.serviceWorkerRegistration = reg;
      window.dispatchEvent(new Event('serviceWorkerRegistration'));
      console.log('Service Worker зарегистрирован успешно');
    } catch (error) {
      console.log('Service Worker регистрация не удалась:', error);
      // Не блокируем работу сайта если SW не работает
    }
  }
});
