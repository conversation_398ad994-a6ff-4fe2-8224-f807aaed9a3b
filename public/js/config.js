// Конфигурация для Magic Quiz Ngeriya
window.gameConfig = {
  // Базовая ссылка для кнопки "Claim Now"
  baseClaimButtonUrl: 'https://vxrqzlp.bid/cl/a79f414663249fda?p1=&p2=&source=FB&site=',

  // Функция для получения финальной ссылки с utm_source
  getClaimButtonUrl: function() {
    const urlParams = new URLSearchParams(window.location.search);
    const utmSource = urlParams.get('utm_source');

    // Если есть utm_source, используем его, иначе используем 'game' по умолчанию
    const siteValue = utmSource || 'game';

    return this.baseClaimButtonUrl + siteValue;
  },

  // Настройки Facebook Pixel
  facebookPixel: {
    enabled: true,
    trackLead: true
  },

  // Настройки кнопки
  button: {
    openInNewTab: false, // открывать ссылку в той же вкладке
    disableAfterClick: true // отключать кнопку после клика
  }
};
