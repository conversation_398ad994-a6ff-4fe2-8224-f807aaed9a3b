class TowerRushGame{constructor(){this.game=document.getElementById("game"),this.gameField=document.getElementById("game-field"),this.gameTower=document.getElementById("game-tower"),this.gameBalance=document.getElementById("game-balance"),this.gameMultiplier=document.getElementById("game-multiplier"),this.gameResults=document.getElementById("game-results"),this.spinButton=document.getElementById("go-btn"),this.cashButton=document.getElementById("game-cash"),this.modal=document.getElementById("modal"),this.effectsContainer=document.getElementById("effects"),this.effectsImage=document.getElementById("effects-image"),this.winSound=new Audio("assets/general/sound/tower-rush-win.mp3"),this.cashSound=new Audio("assets/general/sound/tower-rush-cash.mp3"),this.dropSound=new Audio("assets/general/sound/tower-rush-drop.mp3"),this.rate=this.game.dataset.rate,this.maxSteps=4,this.currentStep=0,this.currentMultiplier=0,this.multipliers=[2.5,.65,1.75,3]}initGame(){this.setNextStep(),window.addEventListener("placementOpenModal",()=>{this.showModal()}),this.spinButton.addEventListener("click",()=>{this.initSpin()}),this.cashButton.addEventListener("click",()=>{this.showEffects(),this.triggerShowModal(),this.playSound(this.cashSound)})}initSpin(){window.isMobile&&window.pushPlacement&&!window.firstClick&&0===this.currentStep?window.dispatchEvent(new CustomEvent("placementFirstClick",{detail:[this.spin.bind(this)]})):this.spin()}async spin(){this.currentStep+=1,this.setStarted(),this.playSound(this.dropSound),this.currentStep<=this.maxSteps&&(this.startStep(),await this.dropBuild(),this.updateResults(),this.updateBalance(),this.stopStep()),this.currentStep<this.maxSteps&&this.setNextStep(),this.currentStep>=this.maxSteps&&(this.setWin(),this.playSound(this.winSound,500),this.showEffects(500),this.triggerShowModal(500))}setWin(){this.game.classList.add("is--win")}setStarted(){this.game.classList.add("is--started")}startStep(){this.game.classList.add("is--active")}stopStep(){this.game.classList.remove("is--active")}setNextStep(){this.game.classList.add(`is--step-${this.currentStep+1}`)}updateBalance(){this.currentMultiplier=this.currentMultiplier+this.multipliers[this.currentStep-1],this.gameBalance.innerText=Number(this.rate*this.currentMultiplier).toFixed(2)}updateResults(){const t=this.multipliers[this.currentStep-1];this.gameMultiplier.innerText=this.multipliers[this.currentStep-1],this.game.classList.add("is--multiplier"),setTimeout(()=>{this.game.classList.remove("is--multiplier");const e=document.createElement("span");e.innerText=`x${t}`,this.gameResults.appendChild(e)},1e3)}async dropBuild(){const t=this.game.querySelector(`.game__field-tower-item.is--${this.currentStep}`),e=this.game.querySelector(`.game__field-tower-item.is--${this.currentStep} .game__field-tower-item-build`),s=this.game.querySelector(`.game__field-tower-item.is--${this.currentStep-1}`),i=this.getElementTransform(t),a=this.getElementTransform(e),n=s.getBoundingClientRect(),r=this.gameField.offsetHeight-n.top;e.style=`transform: translate(${a.x}px, ${a.y}px) rotate(${a.angle}deg); animation: 500ms stableBuild linear forwards;`,t.style=`transform: translate(${i.x}px, ${i.y}px) rotate(${i.angle}deg); animation: 500ms stableBuild linear forwards; bottom: ${r}px;`,await this.waitForTransition(t);const o=document.createElement("img");o.src="assets/towerRush/img/drop.gif",o.classList.add("game__field-tower-drop"),o.style.bottom=`${r}px`,this.gameTower.appendChild(o),setTimeout(()=>{o.remove()},800)}getElementTransform(t){const e=window.getComputedStyle(t).transform;if("none"!==e){const t=e.match(/matrix.*\((.+)\)/)[1].split(", "),s=parseFloat(t[0]),i=parseFloat(t[1]);return{x:parseFloat(t[4]),y:parseFloat(t[5]),angle:Math.round(Math.atan2(i,s)*(180/Math.PI))}}}async waitForTransition(t){return new Promise(e=>{const s=()=>{t.removeEventListener("transitionend",s),e()};t.addEventListener("transitionend",s)})}showEffects(t=0,e=1500){this.effectsContainer.classList.add("visible"),setTimeout(()=>{const t=document.createElement("div");t.style.backgroundImage=`url(${this.effectsImage.src})`,t.classList.add("effects__block"),this.effectsContainer.appendChild(t),setTimeout(()=>{this.effectsContainer.classList.remove("visible"),this.effectsContainer.classList.add("hidden")},e)},t)}showModal(){document.body.classList.add("is--modal-open"),this.modal.classList.add("is--active")}playSound(t,e=0){setTimeout(()=>{t.muted=!1,t.currentTime=0,t.play().catch(t=>{console.error("Помилка при відтворенні аудіо: ",t)})},e)}triggerShowModal(t=0){setTimeout(()=>{window.dispatchEvent(new Event("placementOpenModal"))},t)}}document.addEventListener("DOMContentLoaded",()=>{(new TowerRushGame).initGame()});