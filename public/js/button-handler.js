// Обработчик кнопки "Claim Now" для Magic Quiz Ngeriya
(function() {
  'use strict';
  
  // Ждем загрузки DOM
  document.addEventListener('DOMContentLoaded', function() {
    initClaimButton();
  });
  
  function initClaimButton() {
    const claimButton = document.getElementById('win-button-modal');
    
    if (!claimButton) {
      console.warn('Кнопка "Claim Now" не найдена');
      return;
    }
    
    // Проверяем наличие конфигурации
    if (!window.gameConfig) {
      console.error('Конфигурация gameConfig не найдена');
      return;
    }
    
    // Добавляем обработчик клика
    claimButton.addEventListener('click', handleClaimButtonClick);
    
    console.log('Обработчик кнопки "Claim Now" инициализирован');
  }
  
  function handleClaimButtonClick(event) {
    event.preventDefault();
    
    const config = window.gameConfig;
    const button = event.currentTarget;
    
    try {
      // Отправляем Facebook событие Lead
      if (config.facebookPixel.enabled && config.facebookPixel.trackLead) {
        if (typeof fbq !== 'undefined') {
          fbq('track', 'Lead');
          console.log('Facebook Lead событие отправлено');
        } else {
          console.warn('Facebook Pixel не найден');
        }
      }
      
      // Отключаем кнопку если настроено
      if (config.button.disableAfterClick) {
        button.disabled = true;
        button.classList.add('is--disabled');
        
        // Меняем текст кнопки
        const buttonText = button.querySelector('span');
        if (buttonText) {
          buttonText.textContent = 'Processing...';
        }
      }
      
      // Открываем ссылку
      if (config.claimButtonUrl) {
        if (config.button.openInNewTab) {
          window.open(config.claimButtonUrl, '_blank', 'noopener,noreferrer');
        } else {
          window.location.href = config.claimButtonUrl;
        }
        console.log('Переход по ссылке:', config.claimButtonUrl);
      } else {
        console.error('URL для кнопки не настроен');
      }
      
    } catch (error) {
      console.error('Ошибка при обработке клика кнопки:', error);
      
      // Восстанавливаем кнопку в случае ошибки
      if (config.button.disableAfterClick) {
        button.disabled = false;
        button.classList.remove('is--disabled');
        
        const buttonText = button.querySelector('span');
        if (buttonText) {
          buttonText.textContent = 'Claim Now';
        }
      }
    }
  }
  
})();
