// Service Worker для Magic Quiz Ngeriya
const CACHE_NAME = 'magic-quiz-v1';
const urlsToCache = [
  '/',
  '/css/style.css',
  '/js/config.js',
  '/js/app.min.js',
  '/js/common.min.js',
  '/js/button-handler.js',
  '/js/commonBonuses.min.js',
  '/images/logo.webp',
  '/images/favicon-32x32.webp',
  '/images/apple-touch-icon.webp',
  '/manifest.json'
];

// Установка Service Worker
self.addEventListener('install', function(event) {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(function(cache) {
        return cache.addAll(urlsToCache);
      })
      .catch(function(error) {
        console.log('Cache installation failed:', error);
      })
  );
});

// Активация Service Worker
self.addEventListener('activate', function(event) {
  event.waitUntil(
    caches.keys().then(function(cacheNames) {
      return Promise.all(
        cacheNames.map(function(cacheName) {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

// Обработка запросов
self.addEventListener('fetch', function(event) {
  // Пропускаем запросы к внешним доменам и API
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(function(response) {
        // Возвращаем кешированную версию или загружаем из сети
        return response || fetch(event.request).catch(function() {
          // Если сеть недоступна, возвращаем базовую страницу
          return caches.match('/');
        });
      }
    )
  );
});
